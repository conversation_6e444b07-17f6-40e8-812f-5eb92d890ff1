import React, { useEffect, useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView, Image, ScrollView, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axiosInstance from "../utils/axiosInstance";

const Dashboard = () => {

  const navigation = useNavigation();

  const handlelogout = async () => {
    Alert.alert(
      "Logout",
      "Are you sure you want to logout?",
      [
        { text: "Cancel", style: "cancel" },
        { 
          text: "Logout", 
          style: "destructive",
          onPress: async () => {
            await AsyncStorage.clear();
            setTimeout(() => {
              navigation.navigate("LoginPage");
            }, 100);
          }
        }
      ]
    );
  }

  const [services, setservices] = useState([]);
  const [error, setError] = useState("");
  const [role, setRole] = useState("");
  const [status, setStatus] = useState("");
  const [name, setName] = useState("");
  const [attendance, setAttendance] = useState("");

  useEffect(() => {
    const checkAuth = async () => {
      const token = await AsyncStorage.getItem("token");
      if (!token) {
        await AsyncStorage.clear();
        navigation.navigate("LoginPage");
        return;
      }
      
      const userRole = await AsyncStorage.getItem("role");
      const userStatus = await AsyncStorage.getItem("status");
      const userName = await AsyncStorage.getItem("name");
      const userAttendance = await AsyncStorage.getItem("isattendance");

      setRole(userRole || "");
      setStatus(userStatus || "");
      setName(userName || "");
      setAttendance(userAttendance || "false");
    };
    
    checkAuth();
  }, [])

  useEffect(() => {

    if (status == "new") {
      setservices([
        { name: "Form", screen: "NewStudentForm" },
        { name: "Logout", action: handlelogout }
      ]);
      return;
    }
    if (status == "pending") {
      setError("⚠️ Wait until Admin approval and Logout now...")
      setservices([
        {
          name: "Logout",
          action: handlelogout
        }
      ])
      return;
    }
    if (status == "inactive") {
      setError("⚠️ You are inactive, Logout now...")
      setservices([
        {
          name: "Logout",
          action: handlelogout
        }
      ])
      return;
    }
    if (status == "active") {
      switch (role) {
        case "student":
          if (attendance === "true") {
            setservices([
              { name: "Attendance", screen: "Attendance" },
              { name: "Events", screen: "Event" },
              { name: "Leave", screen: "StudentLeave" },
              { name: "Fine", screen: "StudentFine" },
              { name: "My Profile", screen: "Profile" },
              { name: "Setting", screen: "Setting" },
            ]);
          } else {
            setservices([
              { name: "Attendance", screen: "StudentAttendance" },
              { name: "Leave", screen: "StudentLeave" },
              { name: "Fine", screen: "StudentFine" },
              { name: "My Profile", screen: "Profile" },
              { name: "Setting", screen: "Setting" },
            ]);
          }

          break;

        case "admin":
          
            setservices([
              { name: "Users", screen: "Users" },
              { name: "Attendance", screen: "Attendance" },
              { name: "Leave", screen: "Leave" },
              { name: "Events", screen: "Event" },
              { name: "Fine", screen: "Fine" },
              { name: "My Profile", screen: "Profile" },
              { name: "Setting", screen: "Setting" },
            ]);
         
          break;

        case "leader":
          if (attendance === "true") {
            setservices([
              { name: "Users", screen: "LeaderUsers" },
              { name: "Attendance", screen: "Attendance" },
              { name: "Events", screen: "Event" },
              { name: "Leave", screen: "StudentLeave" },
              { name: "Fine", screen: "StudentFine" },
              { name: "My Profile", screen: "Profile" },
              { name: "Setting", screen: "Setting" },
            ]);
          } else {
            setservices([
              { name: "Users", screen: "LeaderUsers" },
              { name: "Attendance", screen: "StudentAttendance" },
              { name: "Leave", screen: "StudentLeave" },
              { name: "Fine", screen: "StudentFine" },
              { name: "My Profile", screen: "Profile" },
              { name: "Setting", screen: "Setting" },
            ]);
          }
          break;
      }
    }


  }, [status, role, attendance])

  const handleServicePress = (service) => {
    if (service.action) {
      service.action();
    } else if (service.screen) {
      navigation.navigate(service.screen);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.card}>
          <View style={styles.profileSection}>
            <Image
              source={{ uri: "https://ui-avatars.com/api/?name=Jay+Swaminarayan" }}
              style={styles.profileImage}
            />
            <View style={styles.profileInfo}>
              <Text style={styles.greeting}>Jay Swaminarayan</Text>
              <Text style={styles.userName}>{name}</Text>
              <Text style={styles.userRole}>{role}</Text>
            </View>
          </View>

          {error && (
            <View style={[
              styles.errorContainer,
              error.includes('successfully') ? styles.successContainer : styles.errorContainer
            ]}>
              <Text style={[
                styles.errorText,
                error.includes('successfully') ? styles.successText : styles.errorText
              ]}>
                {error}
              </Text>
            </View>
          )}

          <Text style={styles.servicesTitle}>Services</Text>
          <View style={styles.servicesGrid}>
            {services.map((service) => (
              <TouchableOpacity
                key={service.name}
                style={[
                  styles.serviceCard,
                  service.name === "Logout" && styles.logoutCard
                ]}
                onPress={() => handleServicePress(service)}
              >
                <Text style={[
                  styles.serviceText,
                  service.name === "Logout" && styles.logoutText
                ]}>
                  {service.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#a89c8a',
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'flex-start',
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  card: {
    backgroundColor: '#f6f3ef',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    padding: 16,
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  profileInfo: {
    flex: 1,
  },
  greeting: {
    color: '#6366f1',
    fontWeight: '600',
    fontSize: 14,
  },
  userName: {
    color: '#4338ca',
    fontSize: 12,
    fontWeight: '300',
  },
  userRole: {
    color: '#4338ca',
    fontSize: 12,
    fontWeight: '300',
  },
  errorContainer: {
    padding: 12,
    marginBottom: 16,
    borderRadius: 8,
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#fca5a5',
  },
  successContainer: {
    backgroundColor: '#f0fdf4',
    borderColor: '#86efac',
  },
  errorText: {
    fontSize: 14,
    color: '#dc2626',
  },
  successText: {
    color: '#16a34a',
  },
  servicesTitle: {
    fontSize: 16,
    fontWeight: '600',
    paddingHorizontal: 16,
    paddingBottom: 8,
    color: '#374151',
  },
  servicesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    paddingHorizontal: 16,
  },
  serviceCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: '47%',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  logoutCard: {
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#fca5a5',
  },
  serviceText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
    textAlign: 'center',
  },
  logoutText: {
    color: '#dc2626',
  },
});

export default Dashboard;
