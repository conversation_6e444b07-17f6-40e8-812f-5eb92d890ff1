{"expo": {"name": "APC BVN", "slug": "attendance-apcbvn", "version": "1.3.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "platforms": ["ios", "android"], "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#a89c8a"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.attendance.apcbvn"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#a89c8a"}, "package": "com.attendance.apcbvn", "versionCode": 1, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE"]}, "plugins": [], "extra": {"eas": {"projectId": "03d1b5b2-31a1-4c8c-8f92-d35e21a3b226"}}, "owner": "harsh44"}}