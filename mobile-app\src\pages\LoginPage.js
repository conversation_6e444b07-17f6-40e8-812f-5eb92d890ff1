import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, SafeAreaView, Alert, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axiosInstance from "../utils/axiosInstance";
import Passwordinput from "../input/PasswordInput";
import Navbar from "../components/Navbar/Navbar";

const LoginPage = () => {

    const [mobile, setmobile] = useState("");
    const [password, setpassword] = useState("");
    const [error, setError] = useState(null);

    const navigation = useNavigation();

    const handleMobileInput = (value) => {
        // Only allow numeric values
        if (value === '' || /^[0-9\b]+$/.test(value)) {
            setmobile(value);
        }
    };

    const handleLogin = async () => {
        if (!mobile) {
            setError("Please enter your mobile number");
            return;
        }
        if (!password) {
            setError("Please enter the password");
            return;
        }
        setError("")

        //Login API Call

        try {
            const response = await axiosInstance.post("/auth/login", {
                mobile: mobile,
                password: password,
            });

            //handle successfull login
            if (response.data && response.data.accessToken) {
                await AsyncStorage.setItem("token", response.data.accessToken);
                await AsyncStorage.setItem("role", response.data.role || "");
                await AsyncStorage.setItem("status", response.data.status || "");
                await AsyncStorage.setItem("name", response.data.name || "");
                await AsyncStorage.setItem("isattendance", String(response.data.isattendance || false));
                navigation.navigate('Dashboard')
            }

        } catch (error) {
            if (error.response && error.response.data && error.response.data.message) {
                setError(error.response.data.message);
            } else {
                setError("An unexpected error occured. Please try again.")
            }
        }

    }

    return (
        <SafeAreaView style={styles.container}>
            <Navbar />
            <KeyboardAvoidingView 
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={styles.keyboardView}
            >
                <ScrollView contentContainerStyle={styles.scrollContent}>
                    <View style={styles.formContainer}>
                        <Text style={styles.title}>Login</Text>

                        <View style={styles.inputContainer}>
                            <TextInput
                                placeholder='Mobile'
                                keyboardType="numeric"
                                maxLength={10}
                                value={mobile}
                                onChangeText={handleMobileInput}
                                style={styles.input}
                                placeholderTextColor="#999"
                            />
                        </View>

                        <Passwordinput
                            value={password}
                            onChange={(e) => setpassword(e.target.value)}
                        />

                        {error && <Text style={styles.errorText}>{error}</Text>}
                        
                        <TouchableOpacity
                            onPress={handleLogin}
                            style={styles.loginButton}
                        >
                            <Text style={styles.loginButtonText}>Login</Text>
                        </TouchableOpacity>

                        <Text style={styles.footerText}>
                            Not registered yet? Contact Admin
                        </Text>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </SafeAreaView>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f8f9fa',
    },
    keyboardView: {
        flex: 1,
    },
    scrollContent: {
        flexGrow: 1,
        justifyContent: 'center',
        paddingHorizontal: 32,
    },
    formContainer: {
        backgroundColor: 'white',
        padding: 32,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        color: '#374151',
        marginBottom: 24,
    },
    inputContainer: {
        marginBottom: 16,
    },
    input: {
        width: '100%',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderWidth: 1,
        borderColor: '#d1d5db',
        borderRadius: 6,
        fontSize: 16,
        backgroundColor: 'white',
    },
    errorText: {
        color: '#dc2626',
        fontSize: 14,
        marginTop: 8,
    },
    loginButton: {
        width: '100%',
        paddingVertical: 8,
        paddingHorizontal: 16,
        backgroundColor: '#6366f1',
        borderRadius: 6,
        alignItems: 'center',
        marginTop: 16,
    },
    loginButtonText: {
        color: 'white',
        fontWeight: '500',
        fontSize: 16,
    },
    footerText: {
        textAlign: 'center',
        color: '#6b7280',
        marginTop: 16,
    },
});

export default LoginPage;
