import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, SafeAreaView, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/Ionicons';
import { format } from 'date-fns';
import axiosInstance from '../../utils/axiosInstance';
import Calendar from '../../input/Calendar';

const NewStudentForm = () => {
  const navigation = useNavigation();

  const [form, setForm] = useState({
    name: "",
    email: "",
    mobile: "",
    password: "",
    dob: "",
    instituteName: "",
    semester: 1,
    branch: "",
    address: "",
    parentMobile: "",
    roomNo: "",
    rollNo: "",
    role: "student",
    status: "active",
    isattendance: false
  });

  const [errors, setErrors] = useState({});
  const [submitError, setSubmitError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [role, setRole] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  useEffect(() => {
    const getUserRole = async () => {
      const userRole = await AsyncStorage.getItem("role");
      setRole(userRole || "");
    };
    getUserRole();
  }, []);

  const handleBack = async () => {
    const userRole = await AsyncStorage.getItem("role");
    if (userRole === "student") {
      navigation.navigate("Dashboard");
    } else if (userRole === "admin") {
      navigation.navigate("Users");
    }
  };

  const roleOptions = [
    { value: "student", label: "Student" },
    { value: "admin", label: "Admin" },
    { value: "leader", label: "Leader" }
  ];

  const statusOptions = [
    { value: "pending", label: "Pending" },
    { value: "active", label: "Active" },
    { value: "inactive", label: "Inactive" }
  ];

  const semesterOptions = [
    { value: 1, label: "Semester 1" },
    { value: 2, label: "Semester 2" },
    { value: 3, label: "Semester 3" },
    { value: 4, label: "Semester 4" },
    { value: 5, label: "Semester 5" },
    { value: 6, label: "Semester 6" },
    { value: 7, label: "Semester 7" },
    { value: 8, label: "Semester 8" }
  ];

  const handleDateSelect = (date) => {
    setSelectedDate(date);
    const formattedDate = format(date, 'yyyy-MM-dd');
    handleChange('dob', formattedDate);
    setShowCalendar(false);
  };

  const validateField = (name, value) => {
    switch (name) {
      case 'name':
        return !value.trim() ? 'Name is required' :
          !/^[a-zA-Z\s]{2,50}$/.test(value) ? 'Name should only contain letters and spaces (2-50 characters)' : '';

      case 'email':
        return !value ? 'Email is required' :
          !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? 'Invalid email format' : '';

      case 'dob':
        const date = new Date(value);
        const now = new Date();
        const minDate = new Date(now.getFullYear() - 100, now.getMonth(), now.getDate());
        const maxDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        return !value ? 'Date of birth is required' :
          date < minDate || date > maxDate ? 'Age should be between 1 and 100 years' : '';

      case 'instituteName':
        return !value.trim() ? 'Institute name is required' : '';

      case 'semester':
        return !value ? 'Semester is required' :
          value < 1 || value > 8 ? 'Semester should be between 1 and 8' : '';

      case 'branch':
        return !value.trim() ? 'Branch is required' : '';

      case 'parentMobile':
        return !value ? 'Parent mobile is required' :
          !/^[0-9]{10}$/.test(value) ? 'Invalid mobile number (10 digits required)' : '';

      case 'mobile':
        return !value ? 'Mobile is required' :
          !/^[0-9]{10}$/.test(value) ? 'Invalid mobile number (10 digits required)' : '';

      case 'password':
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        return !value ? 'Password is required' :
          value.length < 8 ? 'Password must be at least 8 characters' :
            !passwordRegex.test(value) ?
              'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character' : '';

      case 'role':
        return !value ? 'Role is required' :
          !roleOptions.map(o => o.value).includes(value) ? 'Invalid role selected' : '';

      case 'status':
        return !value ? 'Status is required' :
          !statusOptions.map(o => o.value).includes(value) ? 'Invalid status selected' : '';

      default:
        return '';
    }
  };

  const handleChange = (name, value) => {
    setForm(prev => ({ ...prev, [name]: value }));

    // Validate field on change
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const getFieldsForRole = () => {
    if (role === "student") {
      return [
        { label: "Name", name: "name", type: "text" },
        { label: "Email", name: "email", type: "email" },
        { label: "Date of Birth", name: "dob", type: "calendar" },
        { label: "Institute Name", name: "instituteName", type: "text" },
        { label: "Semester", name: "semester", type: "select", options: semesterOptions },
        { label: "Branch", name: "branch", type: "text" },
        { label: "Address", name: "address", type: "text" },
        { label: "Parent Mobile", name: "parentMobile", type: "tel" }
      ];
    } else if (role === "admin") {
      return [
        { label: "Name", name: "name", type: "text" },
        { label: "Mobile", name: "mobile", type: "tel" },
        { label: "Email", name: "email", type: "email" },
        { label: "Password", name: "password", type: "password-visible" },
        { label: "Date of Birth", name: "dob", type: "calendar" },
        { label: "Institute Name", name: "instituteName", type: "text" },
        { label: "Semester", name: "semester", type: "select", options: semesterOptions },
        { label: "Branch", name: "branch", type: "text" },
        { label: "Address", name: "address", type: "text" },
        { label: "Parent Mobile", name: "parentMobile", type: "tel" },
        { label: "Roll No", name: "rollNo", type: "text" },
        { label: "Room No", name: "roomNo", type: "text" },
        { label: "Role", name: "role", type: "select", options: roleOptions },
        { label: "Status", name: "status", type: "select", options: statusOptions },
        { label: "Attendance", name: "isattendance", type: "checkbox" }
      ];
    }
    return [];
  };

  const validateForm = () => {
    const newErrors = {};
    const fields = getFieldsForRole();

    fields.forEach(field => {
      if (field.type !== 'checkbox') {
        const error = validateField(field.name, form[field.name]);
        if (error) newErrors[field.name] = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    setSubmitError(null);

    if (!validateForm()) {
      setSubmitError("Please fix all errors before submitting");
      return;
    }

    // Create submit form with only visible fields
    const fields = getFieldsForRole();
    const visibleFields = fields.map(f => f.name);
    const submitForm = Object.keys(form)
      .filter(key => visibleFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = form[key];
        return obj;
      }, {});

    try {
      setLoading(true);

      if (role === "student") {
        const response = await axiosInstance.post("/students/register", { form: submitForm });
        if (response.data && !response.data.error) {
          await AsyncStorage.setItem("status", response.data.status);
          if (response.data.status === "pending") {
            Alert.alert(
              "Success",
              "Registration submitted successfully! Please wait for admin approval.",
              [
                {
                  text: "OK",
                  onPress: () => navigation.navigate("Dashboard")
                }
              ]
            );
          }
        }
      } else if (role === "admin") {
        const response = await axiosInstance.post("/admin/register", { form: submitForm });
        if (response.data && !response.data.error) {
          Alert.alert(
            "Success",
            "Student registered successfully!",
            [
              {
                text: "OK",
                onPress: () => navigation.navigate("Users")
              }
            ]
          );
        }
      }
    } catch (error) {
      setSubmitError(
        error.response?.data?.message ||
        "An unexpected error occurred. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const renderInput = (field) => {
    switch (field.type) {
      case 'calendar':
        return (
          <View>
            <TouchableOpacity
              onPress={() => setShowCalendar(!showCalendar)}
              style={styles.dateSelector}
            >
              <View style={styles.dateSelectorContent}>
                <Icon name="calendar-outline" size={20} color="#6b7280" />
                <Text style={styles.dateText}>
                  {form[field.name] ? format(new Date(form[field.name]), 'dd MMM yyyy') : 'Select Date'}
                </Text>
              </View>
              <Icon
                name="chevron-down"
                size={16}
                color="#9ca3af"
                style={[
                  styles.chevron,
                  showCalendar && styles.chevronRotated
                ]}
              />
            </TouchableOpacity>

            <Calendar
              selectedDate={form[field.name] ? new Date(form[field.name]) : selectedDate}
              onDateSelect={handleDateSelect}
              showCalendar={showCalendar}
              onToggleCalendar={setShowCalendar}
            />
          </View>
        );

      case 'select':
        return (
          <View style={styles.pickerContainer}>
            <Text style={styles.pickerLabel}>
              {field.name === 'semester' && form[field.name]
                ? `Semester ${form[field.name]}`
                : field.options?.find(opt => opt.value === form[field.name])?.label || `Select ${field.label}`}
            </Text>
            <TouchableOpacity
              style={styles.pickerButton}
              onPress={() => {
                Alert.alert(
                  `Select ${field.label}`,
                  "",
                  field.options.map(option => ({
                    text: option.label,
                    onPress: () => handleChange(field.name, option.value)
                  })).concat([{ text: "Cancel", style: "cancel" }])
                );
              }}
            >
              <Icon name="chevron-down" size={20} color="#6b7280" />
            </TouchableOpacity>
          </View>
        );

      case 'checkbox':
        return (
          <TouchableOpacity
            style={styles.checkboxContainer}
            onPress={() => handleChange(field.name, !form[field.name])}
          >
            <View style={[styles.checkbox, form[field.name] && styles.checkboxChecked]}>
              {form[field.name] && <Icon name="checkmark" size={16} color="white" />}
            </View>
            <Text style={styles.checkboxLabel}>Enable attendance</Text>
          </TouchableOpacity>
        );

      case 'password-visible':
        return (
          <View style={styles.passwordContainer}>
            <TextInput
              style={[styles.textInput, errors[field.name] && styles.textInputError]}
              value={form[field.name] || ""}
              onChangeText={(value) => handleChange(field.name, value)}
              secureTextEntry={!showPassword}
              placeholder="Password"
              placeholderTextColor="#9ca3af"
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Icon
                name={showPassword ? "eye-outline" : "eye-off-outline"}
                size={20}
                color="#6b7280"
              />
            </TouchableOpacity>
          </View>
        );

      default:
        return (
          <TextInput
            style={[styles.textInput, errors[field.name] && styles.textInputError]}
            value={form[field.name] || ""}
            onChangeText={(value) => handleChange(field.name, value)}
            placeholder={field.label}
            placeholderTextColor="#9ca3af"
            keyboardType={field.type === 'email' ? 'email-address' :
                         field.type === 'tel' ? 'phone-pad' :
                         field.type === 'number' ? 'numeric' : 'default'}
          />
        );
    }
  };

  const fields = getFieldsForRole();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Icon name="arrow-back" size={24} color="#000" />
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.card}>
          <Text style={styles.title}>Student Details</Text>

          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#6366f1" />
            </View>
          )}

          {!loading && fields.map((field) => (
            <View key={field.name} style={styles.inputContainer}>
              <Text style={styles.label}>{field.label}</Text>
              {renderInput(field)}
              {errors[field.name] && (
                <Text style={styles.errorText}>{errors[field.name]}</Text>
              )}
            </View>
          ))}

          {submitError && (
            <View style={styles.errorContainer}>
              <Text style={styles.submitErrorText}>{submitError}</Text>
            </View>
          )}

          {!loading && (
            <TouchableOpacity
              style={[styles.submitButton, loading && styles.submitButtonDisabled]}
              onPress={handleSubmit}
              disabled={loading}
            >
              <Text style={styles.submitButtonText}>
                {loading ? 'Submitting...' : 'Submit Details'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#a89c8a',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#a89c8a',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#000',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#374151',
    textAlign: 'center',
    marginBottom: 24,
  },
  loadingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    backgroundColor: 'white',
    color: '#374151',
    fontSize: 16,
  },
  textInputError: {
    borderColor: '#fca5a5',
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  eyeButton: {
    position: 'absolute',
    right: 12,
    padding: 8,
  },
  pickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    backgroundColor: 'white',
  },
  pickerLabel: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
  },
  pickerButton: {
    padding: 4,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#d1d5db',
    borderRadius: 4,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  checkboxLabel: {
    fontSize: 14,
    color: '#374151',
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    backgroundColor: 'white',
  },
  dateSelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateText: {
    fontSize: 16,
    color: '#374151',
    marginLeft: 8,
  },
  chevron: {
    transform: [{ rotate: '0deg' }],
  },
  chevronRotated: {
    transform: [{ rotate: '180deg' }],
  },
  errorText: {
    fontSize: 12,
    color: '#dc2626',
    marginTop: 4,
  },
  errorContainer: {
    padding: 12,
    marginBottom: 16,
    borderRadius: 8,
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#fca5a5',
  },
  submitErrorText: {
    fontSize: 14,
    color: '#dc2626',
    textAlign: 'center',
  },
  submitButton: {
    paddingVertical: 12,
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  submitButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default NewStudentForm;
